#!/usr/bin/env python3
"""
测试设备批量导入功能
"""

import requests
import json

# API配置
BASE_URL = "http://localhost:8002/xiaozhi"
SERVER_SECRET = "56086f15-a833-4d81-83e7-240e85858541"  # 从数据库获取的server.secret
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {SERVER_SECRET}"
}

def test_batch_import():
    """测试批量导入设备MAC地址"""
    
    # 测试MAC地址列表
    mac_addresses = [
        "AA:BB:CC:DD:EE:01",
        "AA:BB:CC:DD:EE:02", 
        "AA:BB:CC:DD:EE:03",
        "AA:BB:CC:DD:EE:04",
        "AA:BB:CC:DD:EE:05"
    ]
    
    print("🚀 开始测试批量导入功能...")
    print(f"准备导入 {len(mac_addresses)} 个MAC地址:")
    for mac in mac_addresses:
        print(f"  - {mac}")
    
    # 发送批量导入请求
    url = f"{BASE_URL}/device/batch-import"
    
    try:
        response = requests.post(url, json=mac_addresses, headers=HEADERS)
        
        print(f"\n📡 请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 批量导入成功!")
            print(f"📊 完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            if result and 'data' in result:
                data = result['data']
                print(f"📊 导入结果:")
                print(f"  - 总数: {data.get('total', 0)}")
                print(f"  - 成功: {data.get('success', 0)}")
                print(f"  - 失败: {data.get('failed', 0)}")
        else:
            print(f"❌ 批量导入失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_get_enabled_devices():
    """测试获取启用设备列表"""
    
    print("\n🔍 测试获取启用设备列表...")
    
    url = f"{BASE_URL}/device/mac/enabled"
    
    try:
        response = requests.get(url, headers=HEADERS)
        
        print(f"📡 请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            if result and 'data' in result:
                enabled_devices = result['data']
                if enabled_devices:
                    print(f"✅ 获取成功! 共有 {len(enabled_devices)} 个启用设备:")
                    for mac in enabled_devices:
                        print(f"  - {mac}")
                else:
                    print("✅ 获取成功! 但没有启用的设备")
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_batch_import()
    test_get_enabled_devices()
