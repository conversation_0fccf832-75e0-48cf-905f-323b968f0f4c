#!/usr/bin/env python3
"""
MAC地址认证API测试脚本
用于验证MAC地址认证修复是否有效
"""

import requests
import json
import sys

def test_mac_auth_api(mac_address, api_base_url, api_key):
    """测试MAC地址认证API流程"""
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print(f"🧪 测试MAC地址认证API: {mac_address}")
    print(f"📡 API地址: {api_base_url}")
    print("=" * 60)
    
    # 1. 测试checkMacAddress接口
    print("1️⃣ 测试 checkMacAddress 接口...")
    check_url = f"{api_base_url}/device/auth/check/{mac_address}"
    try:
        response = requests.get(check_url, headers=headers, timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            if data.get("code") == 0:
                is_enabled = data.get("data", False)
                print(f"   ✅ 设备状态: {'启用' if is_enabled else '未启用'}")
            else:
                print(f"   ❌ 检查失败: {data.get('msg', '未知错误')}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print()
    
    # 2. 测试authenticateMacAddress接口
    print("2️⃣ 测试 authenticateMacAddress 接口...")
    auth_url = f"{api_base_url}/device/auth/authenticate"
    auth_data = {
        "macAddress": mac_address,
        "clientId": "test_client"
    }
    try:
        response = requests.post(auth_url, headers=headers, json=auth_data, timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            if data.get("code") == 0:
                result = data.get("data", {})
                authenticated = result.get("authenticated", False)
                method = result.get("method", "unknown")
                print(f"   ✅ 认证结果: {'成功' if authenticated else '失败'}")
                print(f"   📋 认证方法: {method}")
            else:
                print(f"   ❌ 认证失败: {data.get('msg', '未知错误')}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print()
    
    # 3. 清理缓存并重新测试
    print("3️⃣ 清理缓存并重新测试...")
    invalidate_url = f"{api_base_url}/device/auth/invalidate/{mac_address}"
    try:
        response = requests.get(invalidate_url, headers=headers, timeout=10)
        if response.status_code == 200:
            print(f"   ✅ 缓存清理成功")
            
            # 重新测试checkMacAddress
            print("   🔄 重新测试 checkMacAddress...")
            response = requests.get(check_url, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    is_enabled = data.get("data", False)
                    print(f"   ✅ 清理缓存后设备状态: {'启用' if is_enabled else '未启用'}")
                else:
                    print(f"   ❌ 清理缓存后检查失败: {data.get('msg')}")
        else:
            print(f"   ❌ 缓存清理失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 缓存清理异常: {e}")

def main():
    # 配置信息
    MAC_ADDRESS = "94:a9:90:29:06:10"  # 问题MAC地址
    API_BASE_URL = "http://**************:8002"  # 测试服务器API地址
    
    # 从环境变量或命令行参数获取API密钥
    import os
    API_KEY = os.getenv("XIAOZHI_API_KEY")
    
    if not API_KEY:
        print("❌ 请设置环境变量 XIAOZHI_API_KEY")
        print("💡 export XIAOZHI_API_KEY=your_actual_api_key")
        sys.exit(1)
    
    test_mac_auth_api(MAC_ADDRESS, API_BASE_URL, API_KEY)
    
    print("\n🎯 测试完成！")
    print("\n📋 如果认证仍然失败，请检查:")
    print("1. 数据库中设备记录是否存在且状态为1")
    print("2. manager-api服务是否已重启（应用代码修复）")
    print("3. xiaozhi-server服务是否已重启（清理Python端缓存）")
    print("4. 检查manager-api日志中的认证记录")

if __name__ == "__main__":
    main()
