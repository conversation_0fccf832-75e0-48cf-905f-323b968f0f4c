#!/usr/bin/env python3
"""
测试自动注册逻辑修复
"""

import requests
import json

# API配置
BASE_URL = "http://localhost:8002/xiaozhi"
SERVER_SECRET = "56086f15-a833-4d81-83e7-240e85858541"  # 从数据库获取的server.secret
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {SERVER_SECRET}"
}

def test_auto_register_logic():
    """测试自动注册逻辑"""
    
    # 测试MAC地址
    test_mac = "BB:CC:DD:EE:FF:01"
    
    print("🚀 开始测试自动注册逻辑...")
    print(f"测试MAC地址: {test_mac}")
    
    # 1. 首先确认设备不在设备表中
    print("\n1️⃣ 检查设备是否已存在...")
    
    # 2. 请求配置（这会触发自动注册逻辑）
    print("\n2️⃣ 请求设备配置（触发自动注册）...")

    url = f"{BASE_URL}/config/agent-models"
    data = {
        "macAddress": test_mac,
        "clientId": "test_client",
        "selectedModule": {
            "default": "default"
        }
    }

    try:
        response = requests.post(url, json=data, headers=HEADERS)
        
        print(f"📡 请求状态码: {response.status_code}")
        print(f"📊 完整响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                print("✅ 配置获取成功!")
                data = result.get('data', {})
                if 'agent' in data:
                    print(f"🤖 智能体信息: {data['agent'].get('agent_name', 'Unknown')}")
                if 'models' in data:
                    print(f"🔧 模型配置: {len(data['models'])} 个模型")
            else:
                print(f"❌ 配置获取失败: {result.get('msg', 'Unknown error')}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 3. 再次检查设备是否已被自动注册
    print("\n3️⃣ 检查设备是否已被自动注册...")
    
    # 4. 再次请求配置，验证逻辑是否正确
    print("\n4️⃣ 再次请求配置，验证逻辑...")
    
    try:
        response = requests.post(url, json=data, headers=HEADERS)
        
        print(f"📡 请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                print("✅ 第二次配置获取成功!")
                print("🎉 自动注册逻辑修复验证成功!")
            else:
                print(f"❌ 第二次配置获取失败: {result.get('msg', 'Unknown error')}")
        else:
            print(f"❌ 第二次请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 第二次请求异常: {e}")

def check_enabled_devices():
    """检查启用设备列表"""
    
    print("\n🔍 检查当前启用设备列表...")
    
    url = f"{BASE_URL}/device/mac/enabled"
    
    try:
        response = requests.get(url, headers=HEADERS)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                enabled_devices = result.get('data', [])
                print(f"✅ 当前有 {len(enabled_devices)} 个启用设备:")
                for mac in enabled_devices:
                    print(f"  - {mac}")
            else:
                print(f"❌ 获取失败: {result.get('msg', 'Unknown error')}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    check_enabled_devices()
    test_auto_register_logic()
    check_enabled_devices()
