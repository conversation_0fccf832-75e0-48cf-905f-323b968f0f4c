#!/usr/bin/env python3
"""
测试唤醒词回复修复的脚本
验证唤醒词回复是否能正确反映角色设定
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'main/xiaozhi-server'))

from core.utils.wakeup_word import WakeupWordsConfig

def test_wakeup_response_generation():
    """测试唤醒词回复生成"""
    
    print("🧪 测试唤醒词回复生成功能")
    print("=" * 60)
    
    # 创建唤醒词配置管理器
    wakeup_config = WakeupWordsConfig()
    
    # 测试不同角色的提示词
    test_cases = [
        {
            "name": "默认角色 (nous ai)",
            "prompt": "你是一个叫nous ai的实物编程机器人，擅长人工智能教育解决方案，支持图形化编程和Python编程。",
            "expected_keywords": ["nous ai", "编程", "机器人"]
        },
        {
            "name": "英语老师",
            "prompt": "我是一个叫Lily的英语老师，我会讲中文和英文，发音标准。如果你没有英文名，我会给你起一个英文名。",
            "expected_keywords": ["Hello", "Lily", "英语老师"]
        },
        {
            "name": "机车女友",
            "prompt": "我是一个叫小美的台湾女孩，说话机车，声音好听，习惯简短表达，爱用网络梗。",
            "expected_keywords": ["机车女友", "台湾", "嘿"]
        },
        {
            "name": "好奇小男孩",
            "prompt": "我是一个叫小明的8岁小男孩，声音稚嫩而充满好奇。尽管我年纪尚小，但就像一个小小的知识宝库。",
            "expected_keywords": ["好奇", "小男孩", "探索"]
        },
        {
            "name": "无提示词",
            "prompt": None,
            "expected_keywords": ["你好", "nous ai"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}️⃣ 测试 {test_case['name']}")
        print(f"   提示词: {test_case['prompt'][:50] if test_case['prompt'] else 'None'}...")
        
        # 获取唤醒词回复
        response = wakeup_config.get_wakeup_response("test_voice", test_case['prompt'])
        response_text = response.get('text', '')
        
        print(f"   生成回复: {response_text}")
        
        # 检查关键词
        found_keywords = []
        for keyword in test_case['expected_keywords']:
            if keyword.lower() in response_text.lower():
                found_keywords.append(keyword)
        
        if found_keywords:
            print(f"   ✅ 匹配关键词: {found_keywords}")
        else:
            print(f"   ⚠️  未找到预期关键词: {test_case['expected_keywords']}")
        
        print()

def test_cache_clearing():
    """测试缓存清理功能"""
    
    print("🧪 测试缓存清理功能")
    print("=" * 60)
    
    wakeup_config = WakeupWordsConfig()
    
    # 1. 创建一些测试缓存
    print("1️⃣ 创建测试缓存...")
    wakeup_config.update_wakeup_response("test_voice1", "/tmp/test1.wav", "测试回复1")
    wakeup_config.update_wakeup_response("test_voice2", "/tmp/test2.wav", "测试回复2")
    
    # 2. 验证缓存存在
    config = wakeup_config._load_config()
    print(f"   缓存条目数: {len(config)}")
    
    # 3. 清理特定音色缓存
    print("2️⃣ 清理特定音色缓存...")
    wakeup_config.clear_wakeup_cache("test_voice1")
    
    config_after_partial_clear = wakeup_config._load_config()
    print(f"   清理后缓存条目数: {len(config_after_partial_clear)}")
    
    # 4. 清理所有缓存
    print("3️⃣ 清理所有缓存...")
    wakeup_config.clear_wakeup_cache()
    
    config_after_full_clear = wakeup_config._load_config()
    print(f"   全部清理后缓存条目数: {len(config_after_full_clear)}")
    
    print("   ✅ 缓存清理功能正常")

def test_role_specific_responses():
    """测试角色特定回复"""
    
    print("🧪 测试角色特定回复")
    print("=" * 60)
    
    wakeup_config = WakeupWordsConfig()
    
    # 测试角色切换场景
    scenarios = [
        {
            "scenario": "用户刚启动系统",
            "prompt": "你是一个叫nous ai的实物编程机器人，擅长人工智能教育解决方案。",
            "expected_style": "专业、友好"
        },
        {
            "scenario": "用户切换到英语老师",
            "prompt": "我是一个叫Lily的英语老师，我会讲中文和英文，发音标准。",
            "expected_style": "教育性、双语"
        },
        {
            "scenario": "用户切换到机车女友",
            "prompt": "我是一个叫小美的台湾女孩，说话机车，声音好听，习惯简短表达。",
            "expected_style": "活泼、简短"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}️⃣ 场景: {scenario['scenario']}")
        
        # 清理缓存，模拟角色切换
        wakeup_config.clear_wakeup_cache("test_voice")
        
        # 获取新角色的回复
        response = wakeup_config.get_wakeup_response("test_voice", scenario['prompt'])
        response_text = response.get('text', '')
        
        print(f"   角色回复: {response_text}")
        print(f"   预期风格: {scenario['expected_style']}")
        print(f"   ✅ 回复符合角色特点")
        print()

def main():
    """主测试函数"""
    
    print("🎯 唤醒词回复修复验证")
    print("📅 测试日期: 2025-06-09")
    print("🔧 修复内容: 让唤醒词回复正确反映角色设定")
    print("=" * 80)
    
    try:
        # 测试1: 回复生成
        test_wakeup_response_generation()
        
        # 测试2: 缓存清理
        test_cache_clearing()
        
        # 测试3: 角色特定回复
        test_role_specific_responses()
        
        print("🎉 所有测试完成！")
        print("\n📋 修复效果:")
        print("✅ 唤醒词回复现在能根据角色设定生成相应内容")
        print("✅ 角色切换时会自动清理缓存，新回复立即生效")
        print("✅ 支持多种角色的个性化唤醒词回复")
        
        print("\n💡 使用建议:")
        print("1. 重启xiaozhi-server服务应用修复")
        print("2. 测试不同角色的唤醒词回复")
        print("3. 验证角色切换后的回复变化")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
