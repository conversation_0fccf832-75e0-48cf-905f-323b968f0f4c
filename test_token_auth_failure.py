#!/usr/bin/env python3
"""
测试Token认证失败时的绑定码处理
"""

import asyncio
import websockets
import json

async def test_token_auth_failure():
    """测试Token认证失败的情况"""
    
    # 模拟一个未绑定的设备
    device_id = "test_device_123456"
    
    # 使用无效的Token
    headers = {
        "device-id": device_id,
        "client-id": device_id,
        "authorization": "Bearer invalid_token_12345"
    }
    
    try:
        print(f"🔗 尝试连接WebSocket服务器...")
        print(f"📱 设备ID: {device_id}")
        print(f"🔑 Token: invalid_token_12345 (故意使用无效Token)")
        
        async with websockets.connect(
            "ws://localhost:8100/xiaozhi/v1/",
            extra_headers=headers,
            timeout=10
        ) as websocket:
            print("✅ WebSocket连接成功")
            
            # 接收欢迎消息
            welcome_msg = await websocket.recv()
            print(f"📨 收到欢迎消息: {welcome_msg}")
            
            # 发送一条测试消息
            test_message = {
                "type": "text",
                "content": "你好"
            }
            
            print(f"📤 发送测试消息: {test_message}")
            await websocket.send(json.dumps(test_message))
            
            # 等待响应
            print("⏳ 等待服务器响应...")
            
            # 接收多条消息
            for i in range(5):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    print(f"📨 收到响应 {i+1}: {response}")
                    
                    # 解析响应
                    try:
                        response_data = json.loads(response)
                        if response_data.get("type") == "stt" and "绑定" in response_data.get("content", ""):
                            print("🎉 成功收到绑定码提示！")
                            break
                    except json.JSONDecodeError:
                        pass
                        
                except asyncio.TimeoutError:
                    print(f"⏰ 等待响应 {i+1} 超时")
                    break
                    
    except websockets.exceptions.ConnectionClosedError as e:
        print(f"❌ WebSocket连接被关闭: {e}")
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ WebSocket连接失败，状态码: {e.status_code}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

if __name__ == "__main__":
    print("🧪 开始测试Token认证失败时的绑定码处理...")
    asyncio.run(test_token_auth_failure())
    print("🏁 测试完成")
