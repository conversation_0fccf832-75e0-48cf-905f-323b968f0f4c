#!/usr/bin/env python3
"""
测试MAC认证模式下的连接
"""

import asyncio
import websockets
import json

async def test_mac_auth():
    """测试MAC认证模式"""
    
    # 模拟一个未注册的MAC地址设备
    mac_address = "FF:FF:FF:FF:FF:FF"
    
    # MAC认证模式不需要Token
    headers = {
        "device-id": mac_address,
        "client-id": mac_address,
        # 不提供authorization header，让系统使用MAC认证
    }
    
    try:
        print(f"🔗 尝试连接WebSocket服务器（MAC认证模式）...")
        print(f"📱 MAC地址: {mac_address}")
        print(f"🔑 认证方式: MAC认证（无Token）")
        
        async with websockets.connect(
            "ws://localhost:8100/xiaozhi/v1/",
            extra_headers=headers,
            timeout=10
        ) as websocket:
            print("✅ WebSocket连接成功")
            
            # 接收欢迎消息
            welcome_msg = await websocket.recv()
            print(f"📨 收到欢迎消息: {welcome_msg}")
            
            # 发送一条listen消息（模拟语音识别结果）
            test_message = {
                "type": "listen",
                "mode": "manual",
                "state": "detect",
                "text": "你好"
            }
            
            print(f"📤 发送测试消息: {test_message}")
            await websocket.send(json.dumps(test_message))
            
            # 等待响应
            print("⏳ 等待服务器响应...")
            
            # 接收多条消息
            for i in range(10):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3)
                    print(f"📨 收到响应 {i+1}: {response}")
                    
                    # 解析响应
                    try:
                        response_data = json.loads(response)
                        if response_data.get("type") == "stt":
                            content = response_data.get("content", "")
                            if "激活" in content or "绑定" in content or "官网" in content:
                                print("🎯 收到MAC认证相关提示！")
                                break
                    except json.JSONDecodeError:
                        pass
                        
                except asyncio.TimeoutError:
                    print(f"⏰ 等待响应 {i+1} 超时")
                    if i >= 5:  # 超过5次超时就退出
                        break
                    
    except websockets.exceptions.ConnectionClosedError as e:
        print(f"❌ WebSocket连接被关闭: {e}")
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ WebSocket连接失败，状态码: {e.status_code}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

if __name__ == "__main__":
    print("🧪 开始测试MAC认证模式...")
    asyncio.run(test_mac_auth())
    print("🏁 测试完成")
