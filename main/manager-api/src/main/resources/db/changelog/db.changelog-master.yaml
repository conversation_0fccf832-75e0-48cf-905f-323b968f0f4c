# 规范约定：
# id生成根据时间时分，文件名对应id，常变数据可以根据模块命名自定义
# 每次对数据表进行改动时，只允许新建新对changeSet，不允许对上一个changeSet配置及文件进行修改
databaseChangeLog:
  - changeSet:
      id: "202503141335"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202503141335.sql
  - changeSet:
      id: "202503141346"
      author: czc
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202503141346.sql
  - changeSet:
      id: "202504082211"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504082211.sql
  - changeSet:
      id: "202504092335"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504092335.sql
  - changeSet:
      id: "202504112044"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504112044.sql
  - changeSet:
      id: "202504112058"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504112058.sql
  - changeSet:
      id: "202504151206"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504151206.sql
  - changeSet:
      id: "202504181536"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504181536.sql
  - changeSet:
      id: "202504221135"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504221135.sql
  - changeSet:
      id: "202504221555"
      author: John
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504221555.sql
  - changeSet:
      id: "202504251422"
      author: jiangkunyin
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504251422.sql
  - changeSet:
      id: "202504291043"
      author: jiangkunyin
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504291043.sql
  - changeSet:
      id: "202504301341"
      author: Goody
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202504301341.sql
  - changeSet:
      id: "202505022134"
      author: Goody
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505022134.sql
  - changeSet:
      id: "202505081146"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505081146.sql
  - changeSet:
      id: "202505091409"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505091409.sql
  - changeSet:
      id: "202505091555"
      author: whosmyqueen
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505091555.sql
  - changeSet:
      id: "202505111914"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505111914.sql
  - changeSet:
      id: "202505122348"
      author: ljwwd2
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505122348.sql
  - changeSet:
      id: "202505142037"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505142037.sql
  - changeSet:
      id: "202505182234"
      author: amen
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505182234.sql
  - changeSet:
      id: "202505201744"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505201744.sql
  - changeSet:
      id: "202505151451"
      author: hsoftxl
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505151451.sql
  - changeSet:
      id: "202505271414"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202505271414.sql
  - changeSet:
      id: "202506010920"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202506010920.sql
  - changeSet:
      id: "202506031639"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202506031639.sql
  - changeSet:
      id: "202506032232"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202506032232.sql
  - changeSet:
      id: "202506051538"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202506051538.sql
  - changeSet:
      id: "202506080955"
      author: hrz
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202506080955.sql
  - changeSet:
      id: "202506061810"
      author: simonchen
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/202506061810.sql
  - changeSet:
      id: "************"
      author: simonchen
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/************.sql
  - changeSet:
      id: "************"
      author: simonchen
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/************.sql
  - changeSet:
      id: "************"
      author: simonchen
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/************.sql
  - changeSet:
      id: "************"
      author: simonchen
      comment: "修复ai_device表id字段长度问题，支持UUID格式"
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/************.sql
  - changeSet:
      id: "************"
      author: simonchen
      comment: "创建MAC地址黑名单表"
      changes:
        - sqlFile:
            encoding: utf8
            path: classpath:db/changelog/************.sql